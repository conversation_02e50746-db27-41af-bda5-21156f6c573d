PRODUCTION-READY SORUNLARI İÇİN PROMPT LİSTESİ (ÇAKIŞMASIZ SIRALAMA - 33 PROMPT)
==========================================================================

PROJE DURUMU: 1.5 yıldır tek spor salonunda sorunsuz çalışıyor. Hedef: Denizli'deki tüm spor salonlarına yayılmak için "tak-çalıştır" sistemi oluşturmak.

⚠️ ÇAKIŞMASIZ SIRALAMA: Her prompt bir öncekinin üzerine inşa edilir. Sı<PERSON>la ilerleyin!

🔥 GRUP 1: TEMEL ALTYAPI (SIRALI - ÇAKIŞMA YOK) (1-3)
==================================================

PROMPT 1: MANUEL ENVIRONMENT MANAGEMENT VE CONNECTION STRING GÜVENLİĞİ (HAYATİ!)
------------------------------------------------------------------------
3 <USER> <GROUP>ıyorum: 1) Local test, 2) Staging (staging.gymkod.com), 3) Production (admin.gymkod.com). 
Her environment geçişinde GymContext.cs'deki connection string'i manuel değiştiriyorum:
- Staging: "Server=localhost;User Id=sa;Password=************;Database=Staging;Trusted_Connection=false;Encrypt=False"
- Production: "Server=localhost;User Id=sa;Password=************;Database=GymProject;Trusted_Connection=false;Encrypt=False"
Ayrıca Program.cs'yi de manuel düzenliyorum. Bu süreç hata riski yüksek, ölçeklenebilir değil ve şifreler kod içinde görünüyor!

Şu anda kurduğum sistem hakkında bilgim var ama environment-based configuration konusunda deneyimim yok. 
Environment variables nasıl çalışır? appsettings.{Environment}.json pattern'i nedir? 
Configuration management nasıl otomatikleştirilir? DevOps best practices neler? GitHub'da şifre nasıl gizlenir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 2: ENVIRONMENT BAZLI CORS VE PROGRAM.CS KONFİGÜRASYONU (HAYATİ!)
--------------------------------------------------------------------
Program.cs'de AllowAnyOrigin() kullanıyorum - bu herhangi bir website'in API'mi kullanabileceği anlamına geliyor! 
3 environment'ım var: Local, Staging (staging.gymkod.com), Production (admin.gymkod.com).
Her environment için farklı CORS policy gerekiyor ama şu anda hepsinde aynı kod var. PROMPT 1'de environment configuration'ı hallettikten sonra şimdi CORS'u environment bazlı yapacağım.

Şu anda kurduğum sistem hakkında bilgim var ama environment-based CORS konusunda deneyimim yok.
Environment bazlı CORS nasıl yapılır? staging.gymkod.com ve admin.gymkod.com için 
güvenli CORS policy'si nasıl tasarlanır? Development vs Production CORS farkları neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 3: DBCONTEXT DI KAYDI VE MULTI-TENANT ISOLATION (HAYATİ!)
--------------------------------------------------------------
GymContext DI'da kayıtlı değil, OnConfiguring'de manuel connection string var. PROMPT 1-2'de environment configuration'ı hallettim, şimdi DbContext'i DI'a kaydedeceğim. 
Sonra çoklu salon yapısına geçeceğim. Tek veritabanında CompanyID ile tenant separation yapacağım. 
Şu anda her API isteğinde hangi salon için geldiğini anlama mekanizmam yok. Bir salon diğer salonun verilerini görebilir - bu felaket olur!

Şu anda kurduğum sistem hakkında bilgim var ama DbContext DI registration ve multi-tenant isolation konusunda deneyimim yok. 
DbContext DI'a nasıl kaydedilir? Tenant isolation nasıl çalışır? CompanyContext pattern'i nedir? Row-level security nasıl implement edilir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

🚨 GRUP 2: GÜVENLİK KATMANI (BAĞIMSIZ) (4-6)
==========================================

PROMPT 4: JWT TOKEN GÜVENLİK SORUNLARI
------------------------------------
SecurityKey appsettings.json'da açık, token rotation yok. 2 dakikalık token süresi çok kısa - salon çalışanları sürekli login olmak zorunda kalacak. 
Çoklu salon ortamında kullanıcı deneyimi kritik. Bu prompt bağımsız, önceki prompt'larla çakışmaz.

Şu anda kurduğum sistem hakkında bilgim var ama JWT security best practices konusunda deneyimim yok. 
JWT token lifecycle nasıl çalışır? Refresh token rotation nedir? Token süresi nasıl belirlenir? SecurityKey nasıl güvenli tutulur?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 5: API INPUT VALİDATİON VE GÜVENLİK AÇIKLARI
-------------------------------------------------
Controller'larda model validation eksik. MemberController, MembershipController gibi endpoint'lerde input validation yok. 
SQL injection, XSS attack riski var. FluentValidation var ama controller seviyesinde uygulanmamış. Bu prompt bağımsız, diğer prompt'larla çakışmaz.

Şu anda kurduğum sistem hakkında bilgim var ama API security konusunda deneyimim yok. 
Input validation nasıl yapılır? SQL injection nasıl önlenir? XSS protection nedir? Model binding security nasıl sağlanır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 6: MOBİL APP GÜVENLİK VE NETWORK AÇIĞI
--------------------------------------------
Mobile app'te baseUrl = 'http://*************:5165/api/' hard-coded IP var ve HTTP kullanıyor (HTTPS değil). 
Production'da bu IP çalışmayacak ve güvenlik riski oluşturuyor. Network kesintilerinde turnike çalışmayacak, offline mode yok. Bu prompt bağımsız.

Şu anda kurduğum sistem hakkında bilgim var ama mobile app network management konusunda deneyimim yok. 
Environment-based API URL nasıl yapılır? HTTPS nasıl implement edilir? Offline mode nasıl çalışır? Mobile app security best practices neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

⚡ GRUP 3: PERFORMANCE KATMANI (SIRALI) (7-9)
==========================================

PROMPT 7: DATABASE İNDEX EKSİKLİĞİ PERFORMANS ÇÖKÜŞÜ
---------------------------------------------------
Çoklu salon ortamında her sorgu CompanyID ile filtrelenmeli ama mevcut indexlerde CompanyID composite indexleri eksik olabilir. 
50+ salon olunca sorgular çok yavaşlayacak, sistem kullanılamaz hale gelecek. PROMPT 3'te tenant isolation hallettikten sonra şimdi performance optimize edeceğim.

Şu anda kurduğum sistem hakkında bilgim var ama database indexing konusunda deneyimim yok. 
Index nedir, neden önemli? Composite index nasıl çalışır? Multi-tenant query pattern'leri için hangi indexler şart? Query performance nasıl ölçülür?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 8: DI LIFETIME MEMORY LEAK RİSKİ
-------------------------------------
AutofacBusinessModule'de DAL sınıfları SingleInstance olarak kayıtlı. Bu yaklaşım memory leak'e neden olabilir ve çoklu salon ortamında sistem çökebilir. 
PROMPT 3'te DbContext DI'ı hallettikten sonra şimdi diğer service'lerin lifetime'larını optimize edeceğim.

Şu anda kurduğum sistem hakkında bilgim var ama DI lifetime management konusunda deneyimim yok. 
SingleInstance vs Scoped vs Transient farkları neler? Memory leak nasıl oluşur? DbContext lifetime neden önemli? Multi-tenant ortamda DI nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 9: N+1 QUERY VE PERFORMANCE BOTTLENECK'LER
------------------------------------------------
EfUserDal'da N+1 query problemi var. Eager loading eksik. Database connection pooling yok. 
PROMPT 7-8'de indexing ve DI'ı hallettikten sonra şimdi query optimization yapacağım.

Şu anda kurduğum sistem hakkında bilgim var ama query optimization konusunda deneyimim yok. 
N+1 query problemi nedir? Eager loading nasıl yapılır? Connection pooling nasıl çalışır? Query performance nasıl optimize edilir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

📊 GRUP 4: OPERASYONEL KATMAN (BAĞIMSIZ) (10-12)
==============================================

PROMPT 10: DATABASE FOREIGN KEY VE CONSTRAINT EKSİKLİKLERİ
--------------------------------------------------------
Migration'larda cascade delete kuralları eksik. Orphan record riski var. Data integrity sorunları olabilir.
Foreign key constraint'ler tam değil. Bu prompt bağımsız, diğer prompt'larla çakışmaz.

Şu anda kurduğum sistem hakkında bilgim var ama database constraints konusunda deneyimim yok.
Foreign key constraints nedir? Cascade delete nasıl çalışır? Data integrity nasıl sağlanır? Orphan record nedir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 11: RASPBERRY PI RATE LİMİTİNG SORUNU
------------------------------------------
Mevcut rate limiting 10 saniyede 1 istek ama yoğun saatlerde çoklu giriş olabilir.
Raspberry Pi'lar sürekli /api/member/scannumber endpoint'ine istek atacak. Rate limiting çok kısıtlayıcı olunca turnike çalışmayacak.

Şu anda kurduğum sistem hakkında bilgim var ama IoT device rate limiting konusunda deneyimim yok.
IoT cihazları için rate limiting neden farklı? Burst traffic nasıl handle edilir? Device-based rate limiting nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 12: MULTI-TENANT CACHE MEMORY SORUNU
-----------------------------------------
MaxCacheSize 10.000 ama 50+ salon olunca yetersiz kalacak. Her salon farklı data pattern'ine sahip olacak.
Cache isolation sağlanmazsa bir salon diğerinin cache'ini bozabilir.

Şu anda kurduğum sistem hakkında bilgim var ama multi-tenant cache management konusunda deneyimim yok.
Cache isolation nasıl çalışır? Memory pressure nasıl yönetilir? Cache eviction policy nedir? Redis vs in-memory cache farkları neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

🎯 GRUP 5: RASPBERRY PI DONANIM (SIRALI) (13-15)
==============================================

PROMPT 13: RASPBERRY PI PYTHON KODU ANALİZİ VE ÖĞRENİM
----------------------------------------------------
Raspberry Pi'da çalışan Python kodumu yapay zeka yazdı ve ben Python bilmiyorum. Kod şu anda çalışıyor ama nasıl çalıştığını anlamıyorum.
TURNSTILE_ENABLED = False olarak ayarlanmış, yani turnike bağlantısı henüz yapılmamış.

Şu anda kurduğum sistem hakkında bilgim var ama Python, Raspberry Pi GPIO, tkinter konusunda deneyimim yok.
Python nasıl çalışır? GPIO pinleri nedir? Tkinter GUI nasıl çalışır? Linux'ta servis olarak çalıştırma nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 14: TURNİKE DONANIM BAĞLANTISI VE KABLOLAMA
------------------------------------------------
TURNSTILE_ENABLED = False ve turnike fiziksel olarak bağlı değil. GPIO pin 18 kullanılacak ama hangi kablolar gerekli?
Röle modülü gerekli mi? Güç kaynağı nasıl olmalı? PROMPT 13'te Python kodunu anladıktan sonra şimdi fiziksel bağlantıyı yapacağım.

Şu anda kurduğum sistem hakkında bilgim var ama elektronik, kablolama, GPIO wiring konusunda deneyimim yok.
GPIO pinleri nasıl çalışır? Röle modülü nedir, neden gerekli? Elektriksel güvenlik nasıl sağlanır? Turnike çeşitleri neler?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 15: RASPBERRY PI PRODUCTION DEPLOYMENT
--------------------------------------------
Manuel başlatıyorum (cd qr_turnstile_new, source venv/bin/activate, python qr_turnstile_control.py). Bu production için uygun değil.
PROMPT 13-14'te kod ve donanımı hallettikten sonra şimdi production deployment yapacağım.

Şu anda kurduğum sistem hakkında bilgim var ama Linux systemd, service management konusunda deneyimim yok.
Systemd service nedir? Auto-start nasıl yapılır? Process monitoring nasıl çalışır? Remote access nasıl sağlanır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

🚀 GRUP 6: UZUN VADELİ HAZIRLIK (BAĞIMSIZ) (16-20)
===============================================

PROMPT 16: 7/24 UPTIME VE HEALTH MONİTORİNG
-----------------------------------------
Salonlar 7/24 çalışacak. Sistem çökerse salon işleri durur. Proactive monitoring, alerting, auto-healing gerekiyor.
Şu anda sadece basit /api/health endpoint'im var.

Şu anda kurduğum sistem hakkında bilgim var ama health monitoring konusunda deneyimim yok.
Health check'ler nasıl kurulur? Database, cache health nasıl izlenir? Uptime SLA nasıl garanti edilir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 17: CENTRALIZED LOGGİNG VE ERROR TRACKİNG
----------------------------------------------
Tüm salonların log'larını merkezi izlemem gerekiyor. Hangi salonda ne hata oluyor? FileLoggerService ile basic logging yapıyorum ama structured logging yok.

Şu anda kurduğum sistem hakkında bilgim var ama centralized logging konusunda deneyimim yok.
Structured logging nedir? ELK Stack nasıl çalışır? Error alerting nasıl kurulur? Correlation ID nedir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 18: OTOMATIK BACKUP VE DISASTER RECOVERY
---------------------------------------------
Her salon için otomatik backup gerekiyor. Salon verileri kaybolursa iş durur. Point-in-time recovery, cross-region backup, disaster recovery planı yok.

Şu anda kurduğum sistem hakkında bilgim var ama backup strategies konusunda deneyimim yok.
Automated backup nasıl kurulur? Point-in-time recovery nedir? RTO/RPO değerleri nasıl belirlenir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 19: LOAD BALANCİNG VE AUTO-SCALİNG
---------------------------------------
Tek instance ile başlayacağım ama büyüdükçe horizontal scaling gerekecek. Peak saatlerde (akşam 18-21) tüm salonlar aynı anda yoğun kullanım yapacak.

Şu anda kurduğum sistem hakkında bilgim var ama load balancing konusunda deneyimim yok.
Load balancer nasıl çalışır? Auto-scaling nasıl kurulur? Session management nasıl yapılır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 20: RASPBERRY PI MASS DEPLOYMENT VE IOT YÖNETİMİ
-----------------------------------------------------
Denizli'deki tüm salonlarda Raspberry Pi'lar olacak. Remote monitoring, firmware update, troubleshooting, device health check gerekiyor.
Cihaz çökerse salon girişleri durur.

Şu anda kurduğum sistem hakkında bilgim var ama IoT device management konusunda deneyimim yok.
Remote monitoring nasıl yapılır? Firmware update nasıl otomatikleştirilir? Device health nasıl izlenir? SD kart image nasıl oluşturulur?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

💼 GRUP 7: SATIŞ VE İŞ GELİŞTİRME (BAĞIMSIZ) (21-25)
=================================================

PROMPT 21: FİZİKSEL KURULUM VE SALON ONBOARDING
--------------------------------------------
Raspberry Pi kurulumu, QR kod okuyucu montajı, kart sistemi entegrasyonu, network konfigürasyonu, staff training nasıl yapılmalı?
Salon sahibi teknik bilgili değilse kurulum nasıl basitleştirilmeli?

Şu anda kurduğum sistem hakkında bilgim var ama physical installation konusunda deneyimim yok.
Kurulum süreci nasıl standardize edilir? Remote support nasıl verilir? Training materyalleri nasıl hazırlanır?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 22: QR vs KART SİSTEMİ FİYATLANDIRMA STRATEJİSİ
--------------------------------------------------
Salon sahipleri "QR mu kart mı?" diye soracak, ikisini de isteyebilir. QR sistemi (sadece Raspberry Pi), kart sistemi (ek donanım), hibrit sistem (her ikisi) için farklı fiyatlandırma gerekiyor.

Şu anda kurduğum sistem hakkında bilgim var ama pricing strategy konusunda deneyimim yok.
Feature-based pricing nasıl yapılır? Donanım maliyetleri nasıl hesaplanır? Upgrade/downgrade senaryoları nasıl yönetilir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 23: BILLING VE PAYMENT INTEGRATION
----------------------------------------
Aylık/yıllık subscription model gerekiyor. Otomatik billing, payment processing, invoice generation, dunning management gerekiyor.

Şu anda kurduğum sistem hakkında bilgim var ama payment integration konusunda deneyimim yok.
Payment gateway nasıl entegre edilir? Subscription billing nasıl çalışır? Dunning management nedir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 24: BUSINESS INTELLIGENCE VE REPORTING
--------------------------------------------
Salon sahipleri için QR vs kart kullanım istatistikleri, cihaz performance metrics, giriş-çıkış analytics gerekiyor.
Hangi sistem daha çok kullanılıyor? Cihaz uptime'ı nedir?

Şu anda kurduğum sistem hakkında bilgim var ama business intelligence konusunda deneyimim yok.
Analytics dashboard nasıl tasarlanır? KPI'lar nasıl belirlenir? Reporting tools nasıl entegre edilir?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.

PROMPT 25: CUSTOMER SUPPORT VE HELPDESK SİSTEMİ
---------------------------------------------
1000+ salon olunca support ticket'ları patlar. Otomatik ticket routing, knowledge base, remote troubleshooting gerekiyor.

Şu anda kurduğum sistem hakkında bilgim var ama customer support systems konusunda deneyimim yok.
Helpdesk sistemi nasıl kurulur? Ticket routing nasıl çalışır? Knowledge base nasıl oluşturulur?

Lütfen önce bu konuları detaylı açıkla ki anlayayım, sonra "başla" dediğimde uygulamaya geçelim.
